<?php
/**
 * Main Dashboard - Novel Translation Application
 */

require_once 'config/config.php';
require_once 'includes/header.php';

renderHeader('Dashboard');
?>

<?php
function renderNavigation_called() {} // Prevent auto-render
include 'includes/navigation.php';
renderNavigation('dashboard');
?>

<div class="container mt-4">
    <!-- Preview Section -->
    <section id="preview-section" class="mb-5">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-search me-2"></i>
                    Preview Novel
                </h4>
            </div>
            <div class="card-body">
                <form id="preview-form">
                    <div class="mb-3">
                        <label for="novel-url" class="form-label">Novel URL</label>
                        <input type="url" class="form-control" id="novel-url"
                               placeholder="Enter novel URL (Kakuyomu, Syosetu, or 69书吧)" required>
                        <div class="form-text">
                            Supported platforms:
                            <span class="badge bg-secondary me-1">kakuyomu.jp</span>
                            <span class="badge bg-secondary me-1">ncode.syosetu.com</span>
                            <span class="badge bg-secondary me-1">69shuba.cx</span>
                            <span class="badge bg-secondary me-1">booktoki468.com</span>
                            <br>
                            <small class="text-muted">
                                Can't crawl your novel? Try
                                <a href="manual-entry.php" class="text-decoration-none">manual entry</a>
                            </small>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>
                        Preview Novel
                    </button>
                </form>
            </div>
        </div>

        <!-- Preview Results -->
        <div id="preview-results" class="mt-4" style="display: none;">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-book me-2"></i>
                        Novel Preview
                    </h5>
                </div>
                <div class="card-body">
                    <div id="novel-info"></div>
                    <div class="mt-3">
                        <button id="save-novel-btn" class="btn btn-success me-2">
                            <i class="fas fa-save me-2"></i>
                            Save Novel
                        </button>
                        <button id="cancel-preview-btn" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Saved Novels Section -->
    <section id="novels-section" class="mb-5">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4 class="mb-0">
                <i class="fas fa-library me-2"></i>
                My Novels
            </h4>
            <div>
                <a href="novels.php" class="btn btn-outline-primary btn-sm me-2">
                    <i class="fas fa-list me-1"></i>
                    View All
                </a>
                <button id="refresh-novels-btn" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-sync-alt me-1"></i>
                    Refresh
                </button>
            </div>
        </div>
        <div id="novels-list">
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading novels...</p>
            </div>
        </div>
    </section>

        <!-- Help Section -->
        <section id="help-section" class="mb-5">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        How to Use
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Supported Websites</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Kakuyomu (kakuyomu.jp)</li>
                                <li><i class="fas fa-check text-success me-2"></i>Syosetu (ncode.syosetu.com)</li>
                                <li><i class="fas fa-check text-success me-2"></i>69书吧 (69shuba.cx)</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Features</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-language text-primary me-2"></i>AI-powered translation</li>
                                <li><i class="fas fa-users text-primary me-2"></i>Name consistency tracking</li>
                                <li><i class="fas fa-edit text-primary me-2"></i>Editable translations</li>
                                <li><i class="fas fa-database text-primary me-2"></i>Chapter management</li>
                            </ul>
                        </div>
                    </div>
                    <hr>
                    <h5>Usage Steps</h5>
                    <ol>
                        <li>Enter a novel URL from one of the supported platforms</li>
                        <li>Preview the novel information and chapter list</li>
                        <li>Save the novel to your library</li>
                        <li>Use "Save" and "Translate" buttons for individual chapters</li>
                        <li>Edit name dictionary for translation consistency</li>
                    </ol>
                </div>
            </div>
        </section>
</div>

<?php renderFooter(['assets/js/dashboard.js']); ?>
