<?php
/**
 * ZenRows API Configuration
 * Novel Translation Application
 * 
 * This file contains ZenRows API settings that can be easily updated
 * when switching from trial to paid plans or changing API providers.
 */

// ZenRows API Configuration
return [
    // API Authentication
    'api_key' => '3dfd6205f79bbe078e3266970464a7c9e86c1a40', // Update this when trial expires
    'api_url' => 'https://api.zenrows.com/v1/',
    
    // Default Request Settings
    'default_settings' => [
        'js_render' => true,        // Enable JavaScript rendering for dynamic content
        'premium_proxy' => true,    // Use premium proxies for anti-bot protection
        'wait' => 3000,            // Wait 3 seconds after page load
        'block_resources' => 'image,font,media', // Block unnecessary resources for speed
        'original_status' => true,  // Return original HTTP status codes
    ],
    
    // Platform-specific Settings
    'platform_settings' => [
        'booktoki' => [
            'js_render' => true,
            'premium_proxy' => true,
            'wait' => 8000,            // Booktoki may need more time to load (increased)
            'wait_for' => '.novel-info, .book-info, .novel-content, .chapter-content', // Wait for content elements
            'timeout' => 180,          // Extended timeout for Booktoki (3 minutes)
            'max_retries' => 6,        // More retries for Booktoki
            'retry_delay' => 5,        // Longer delay between retries
            'custom_headers' => [
                'Accept-Language' => 'ko-KR,ko;q=0.9,en;q=0.8',
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Cache-Control' => 'no-cache',
                'Pragma' => 'no-cache'
            ]
        ]
    ],
    
    // Request Limits and Timeouts
    'limits' => [
        'timeout' => 120,          // Request timeout in seconds (increased for Booktoki)
        'max_retries' => 5,        // Maximum retry attempts (increased)
        'retry_delay' => 3,        // Delay between retries in seconds
        'max_response_size' => 10 * 1024 * 1024, // 10MB max response size
        'connect_timeout' => 30,   // Connection timeout in seconds
    ],
    
    // Error Handling
    'error_handling' => [
        'allowed_status_codes' => '404,403,500', // Return content even on these errors
        'log_requests' => true,    // Log all API requests for debugging
        'log_responses' => false,  // Log response content (disable for production)
    ],
    
    // Cost Management
    'cost_settings' => [
        'enable_cost_tracking' => true,  // Track API usage costs
        'daily_request_limit' => 1000,   // Daily request limit (adjust based on plan)
        'warn_at_percentage' => 80,      // Warn when reaching 80% of daily limit
    ]
];
