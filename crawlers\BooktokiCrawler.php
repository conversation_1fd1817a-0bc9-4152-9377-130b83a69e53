<?php
/**
 * Booktoki (booktoki468.com) Crawler
 * Novel Translation Application
 * 
 * Uses ZenRows Universal Scraper API to bypass anti-bot protection
 */

class BooktokiCrawler extends BaseCrawler {
    
    private const BASE_URL = 'https://booktoki468.com';
    private $zenrowsClient;
    
    public function __construct() {
        parent::__construct();
        $this->zenrowsClient = new ZenRowsApiClient();
    }
    
    /**
     * Validate Booktoki URL
     */
    protected function validateUrl(string $url): bool {
        return preg_match('/booktoki\d+\.com\/novel\/\d+/', $url) === 1;
    }
    
    /**
     * Get novel information from Booktoki
     */
    public function getNovelInfo(string $url): array {
        if (!$this->validateUrl($url)) {
            throw new Exception("Invalid Booktoki URL: {$url}");
        }
        
        $this->log("Fetching novel info from: {$url}");
        
        try {
            $response = $this->zenrowsClient->makeRequest($url, [
                'platform' => 'booktoki',
                'wait_for' => '.novel-info, .book-info'
            ]);
            
            $html = $response['content'];
            $dom = $this->parseHtml($html);
            
            // Extract novel information
            $title = $this->extractTitle($dom);
            $author = $this->extractAuthor($dom);
            $synopsis = $this->extractSynopsis($dom);
            $publishDate = $this->extractPublishDate($dom);
            $totalChapters = $this->extractTotalChapters($dom);
            
            $this->log("Successfully extracted novel info: {$title}");
            
            return [
                'platform' => 'booktoki',
                'url' => $url,
                'original_title' => $title,
                'author' => $author,
                'original_synopsis' => $synopsis,
                'publication_date' => $publishDate,
                'total_chapters' => $totalChapters,
                'language' => 'ko'
            ];
            
        } catch (Exception $e) {
            $this->log("Error fetching novel info: " . $e->getMessage(), 'error');
            throw $e;
        }
    }
    
    /**
     * Get chapter list from Booktoki
     */
    public function getChapterList(string $url): array {
        if (!$this->validateUrl($url)) {
            throw new Exception("Invalid Booktoki URL: {$url}");
        }

        $this->log("Fetching chapter list from: {$url}");

        try {
            $response = $this->zenrowsClient->makeRequest($url, [
                'platform' => 'booktoki',
                'wait_for' => '.chapter-list, .episode-list'
            ]);
            
            $html = $response['content'];
            $dom = $this->parseHtml($html);

            $chapters = [];

            // Try multiple selectors for chapter links
            $selectors = [
                '.chapter-list a',
                '.episode-list a',
                '.list-episode a',
                '.novel-episode a',
                'a[href*="/novel/"][href*="/episode/"]'
            ];

            foreach ($selectors as $selector) {
                $chapterElements = $this->querySelectorAll($dom, $selector);
                if ($chapterElements->length > 0) {
                    $this->log("Found chapters using selector: {$selector}");
                    break;
                }
            }

            if (!isset($chapterElements) || $chapterElements->length === 0) {
                throw new Exception("No chapters found on the page");
            }

            foreach ($chapterElements as $index => $element) {
                $chapterUrl = $element->getAttribute('href');
                $chapterTitle = trim($element->textContent);

                // Convert relative URLs to absolute
                if (strpos($chapterUrl, 'http') !== 0) {
                    $chapterUrl = self::BASE_URL . $chapterUrl;
                }

                // Skip if URL doesn't look like a chapter
                if (!preg_match('/\/novel\/\d+\/episode\/\d+/', $chapterUrl)) {
                    continue;
                }

                $chapters[] = [
                    'chapter_number' => $index + 1,
                    'original_title' => processTitleText($chapterTitle),
                    'chapter_url' => $chapterUrl
                ];
            }

            $this->log("Found " . count($chapters) . " chapters");
            return $chapters;

        } catch (Exception $e) {
            $this->log("Error fetching chapter list: " . $e->getMessage(), 'error');
            throw $e;
        }
    }
    
    /**
     * Get chapter content from Booktoki
     */
    public function getChapterContent(string $chapterUrl): array {
        $this->log("Fetching chapter content from: {$chapterUrl}");
        
        try {
            $response = $this->zenrowsClient->makeRequest($chapterUrl, [
                'platform' => 'booktoki',
                'wait_for' => '.chapter-content, .episode-content, .novel-content'
            ]);
            
            $html = $response['content'];
            $dom = $this->parseHtml($html);
            
            // Extract chapter title
            $title = $this->extractChapterTitle($dom);
            
            // Extract chapter content
            $contentElement = $this->findContentElement($dom);
            if (!$contentElement) {
                throw new Exception("Chapter content not found");
            }
            
            $content = $this->extractChapterText($contentElement);
            
            $this->log("Successfully extracted chapter content");
            
            return [
                'original_title' => $title,
                'original_content' => $content,
                'word_count' => mb_strlen($content)
            ];
            
        } catch (Exception $e) {
            $this->log("Error fetching chapter content: " . $e->getMessage(), 'error');
            throw $e;
        }
    }
    
    /**
     * Extract novel title
     */
    private function extractTitle(DOMDocument $dom): string {
        $selectors = [
            '.novel-title',
            '.book-title',
            '.title',
            'h1',
            '.novel-info h1',
            '.book-info h1'
        ];
        
        foreach ($selectors as $selector) {
            $element = $this->querySelector($dom, $selector);
            if ($element) {
                $title = trim($element->textContent);
                if (!empty($title)) {
                    return processTitleText($title);
                }
            }
        }
        
        throw new Exception("Novel title not found");
    }
    
    /**
     * Extract author name
     */
    private function extractAuthor(DOMDocument $dom): string {
        $selectors = [
            '.author',
            '.novel-author',
            '.book-author',
            '.writer',
            '.novel-info .author',
            '.book-info .author'
        ];
        
        foreach ($selectors as $selector) {
            $element = $this->querySelector($dom, $selector);
            if ($element) {
                $author = trim($element->textContent);
                if (!empty($author)) {
                    return processTitleText($author);
                }
            }
        }
        
        return 'Unknown Author';
    }
    
    /**
     * Extract novel synopsis
     */
    private function extractSynopsis(DOMDocument $dom): string {
        $selectors = [
            '.novel-description',
            '.book-description',
            '.synopsis',
            '.summary',
            '.novel-info .description',
            '.book-info .description'
        ];
        
        foreach ($selectors as $selector) {
            $element = $this->querySelector($dom, $selector);
            if ($element) {
                $synopsis = trim($element->textContent);
                if (!empty($synopsis)) {
                    return $synopsis;
                }
            }
        }
        
        return '';
    }
    
    /**
     * Extract publication date
     */
    private function extractPublishDate(DOMDocument $dom): ?string {
        $selectors = [
            '.publish-date',
            '.created-date',
            '.date',
            '.novel-info .date',
            '.book-info .date'
        ];
        
        foreach ($selectors as $selector) {
            $element = $this->querySelector($dom, $selector);
            if ($element) {
                $dateText = trim($element->textContent);
                if (!empty($dateText)) {
                    // Try to parse Korean date format
                    if (preg_match('/(\d{4})[-년]\s*(\d{1,2})[-월]\s*(\d{1,2})/', $dateText, $matches)) {
                        return $matches[1] . '-' . str_pad($matches[2], 2, '0', STR_PAD_LEFT) . '-' . str_pad($matches[3], 2, '0', STR_PAD_LEFT);
                    }
                }
            }
        }
        
        return null;
    }
    
    /**
     * Extract total chapters count
     */
    private function extractTotalChapters(DOMDocument $dom): int {
        // Count chapter links
        $selectors = [
            '.chapter-list a',
            '.episode-list a',
            '.list-episode a'
        ];
        
        foreach ($selectors as $selector) {
            $elements = $this->querySelectorAll($dom, $selector);
            if ($elements->length > 0) {
                return $elements->length;
            }
        }
        
        return 0;
    }
    
    /**
     * Extract chapter title
     */
    private function extractChapterTitle(DOMDocument $dom): string {
        $selectors = [
            '.chapter-title',
            '.episode-title',
            'h1',
            '.title',
            '.chapter-info h1'
        ];
        
        foreach ($selectors as $selector) {
            $element = $this->querySelector($dom, $selector);
            if ($element) {
                $title = trim($element->textContent);
                if (!empty($title)) {
                    return processTitleText($title);
                }
            }
        }
        
        return 'Untitled Chapter';
    }
    
    /**
     * Find chapter content element
     */
    private function findContentElement(DOMDocument $dom): ?DOMElement {
        $selectors = [
            '.chapter-content',
            '.episode-content',
            '.novel-content',
            '.content',
            '.chapter-body',
            '.episode-body'
        ];
        
        foreach ($selectors as $selector) {
            $element = $this->querySelector($dom, $selector);
            if ($element) {
                return $element;
            }
        }
        
        return null;
    }
    
    /**
     * Extract text content from chapter element
     */
    private function extractChapterText(DOMElement $element): string {
        // Remove script and style elements
        $scripts = $element->getElementsByTagName('script');
        for ($i = $scripts->length - 1; $i >= 0; $i--) {
            $scripts->item($i)->parentNode->removeChild($scripts->item($i));
        }
        
        $styles = $element->getElementsByTagName('style');
        for ($i = $styles->length - 1; $i >= 0; $i--) {
            $styles->item($i)->parentNode->removeChild($styles->item($i));
        }
        
        // Get text content and clean it
        $content = $element->textContent;
        
        // Clean up whitespace while preserving paragraph breaks
        $content = preg_replace('/\r\n|\r|\n/', "\n", $content);
        $content = preg_replace('/\n\s*\n/', "\n\n", $content);
        $content = preg_replace('/[ \t]+/', ' ', $content);
        
        return trim($content);
    }
}
