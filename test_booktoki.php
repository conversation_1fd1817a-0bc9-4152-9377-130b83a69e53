<?php
/**
 * Test script for Booktoki Crawler with ZenRows API
 * Novel Translation Application
 */

require_once 'config/config.php';
require_once 'classes/BaseCrawler.php';
require_once 'classes/ZenRowsApiClient.php';
require_once 'crawlers/BooktokiCrawler.php';

echo "=== Booktoki Crawler Test (ZenRows API) ===\n";
echo "Testing ZenRows integration with booktoki468.com\n\n";

try {
    // Initialize crawler
    $crawler = new BooktokiCrawler();
    
    // Test URL - you can replace this with an actual Booktoki novel URL
    $testUrl = 'https://booktoki468.com/novel/12345'; // Replace with actual URL
    
    echo "Test URL: {$testUrl}\n";
    echo "Note: Replace with actual Booktoki novel URL for testing\n\n";
    
    // Test ZenRows API client first
    echo "=== Testing ZenRows API Client ===\n";
    $zenrowsClient = new ZenRowsApiClient();
    
    // Test with a simple page first
    $testResponse = $zenrowsClient->makeRequest('https://httpbin.org/html', [
        'js_render' => false,
        'premium_proxy' => false
    ]);
    
    if ($testResponse['success']) {
        echo "✓ ZenRows API client working correctly\n";
        echo "Response status: {$testResponse['status_code']}\n";
        echo "Content length: " . strlen($testResponse['content']) . " bytes\n";
        echo "Request cost: {$testResponse['cost']}\n";
        echo "Request ID: {$testResponse['request_id']}\n\n";
    } else {
        echo "✗ ZenRows API client test failed\n\n";
    }
    
    // Display usage stats
    $stats = $zenrowsClient->getUsageStats();
    echo "Usage Statistics:\n";
    echo "- Requests made: {$stats['requests_made']}\n";
    echo "- Total cost: {$stats['total_cost']}\n";
    echo "- Daily limit: {$stats['daily_limit']}\n";
    echo "- Remaining: {$stats['remaining_requests']}\n\n";
    
    echo "=== Testing Booktoki Crawler ===\n";
    echo "Note: The following tests require actual Booktoki URLs\n";
    echo "Replace the test URL above with a real novel URL to test\n\n";
    
    // Validate URL format
    if (preg_match('/booktoki\d+\.com\/novel\/\d+/', $testUrl)) {
        echo "✓ URL format validation passed\n";
        
        // Test novel info extraction (commented out to avoid API usage without real URL)
        /*
        echo "\n--- Testing Novel Info Extraction ---\n";
        $novelInfo = $crawler->getNovelInfo($testUrl);
        
        echo "Novel Title: " . $novelInfo['original_title'] . "\n";
        echo "Author: " . $novelInfo['author'] . "\n";
        echo "Language: " . $novelInfo['language'] . "\n";
        echo "Platform: " . $novelInfo['platform'] . "\n";
        echo "Total Chapters: " . $novelInfo['total_chapters'] . "\n";
        echo "Synopsis: " . substr($novelInfo['original_synopsis'], 0, 200) . "...\n";
        
        echo "\n--- Testing Chapter List Extraction ---\n";
        $chapters = $crawler->getChapterList($testUrl);
        
        echo "Found " . count($chapters) . " chapters\n";
        
        if (!empty($chapters)) {
            echo "\nFirst 3 chapters:\n";
            for ($i = 0; $i < min(3, count($chapters)); $i++) {
                $chapter = $chapters[$i];
                echo "Chapter " . $chapter['chapter_number'] . ": " . $chapter['original_title'] . "\n";
                echo "URL: " . $chapter['chapter_url'] . "\n\n";
            }
            
            // Test chapter content extraction
            echo "--- Testing Chapter Content Extraction ---\n";
            $firstChapter = $chapters[0];
            $chapterContent = $crawler->getChapterContent($firstChapter['chapter_url']);
            
            echo "Chapter Title: " . $chapterContent['original_title'] . "\n";
            echo "Word Count: " . $chapterContent['word_count'] . "\n";
            echo "Content Preview: " . substr($chapterContent['original_content'], 0, 300) . "...\n";
        }
        */
        
    } else {
        echo "✗ URL format validation failed\n";
        echo "Expected format: booktoki[number].com/novel/[number]\n";
    }
    
    echo "\n=== Configuration Test ===\n";
    
    // Test configuration loading
    $configFile = APP_ROOT . '/config/zenrows-config.php';
    if (file_exists($configFile)) {
        echo "✓ ZenRows configuration file found\n";
        
        $config = require $configFile;
        echo "✓ Configuration loaded successfully\n";
        echo "API Key configured: " . (empty($config['api_key']) ? "No" : "Yes") . "\n";
        echo "Default JS rendering: " . ($config['default_settings']['js_render'] ? "Enabled" : "Disabled") . "\n";
        echo "Default premium proxy: " . ($config['default_settings']['premium_proxy'] ? "Enabled" : "Disabled") . "\n";
        echo "Booktoki-specific settings: " . (isset($config['platform_settings']['booktoki']) ? "Configured" : "Not configured") . "\n";
    } else {
        echo "✗ ZenRows configuration file not found\n";
    }
    
    echo "\n=== Platform Support Test ===\n";
    
    // Test platform support in main config
    $platforms = SUPPORTED_PLATFORMS;
    if (isset($platforms['booktoki'])) {
        echo "✓ Booktoki platform registered in main config\n";
        echo "Platform name: " . $platforms['booktoki']['name'] . "\n";
        echo "Base URL: " . $platforms['booktoki']['base_url'] . "\n";
        echo "Language: " . $platforms['booktoki']['language'] . "\n";
        echo "URL pattern: " . $platforms['booktoki']['pattern'] . "\n";
    } else {
        echo "✗ Booktoki platform not found in main config\n";
    }
    
    echo "\n=== Test Complete ===\n";
    echo "To fully test the crawler:\n";
    echo "1. Replace the test URL with an actual Booktoki novel URL\n";
    echo "2. Uncomment the novel info and chapter extraction tests\n";
    echo "3. Run the script again\n";
    echo "4. Monitor the logs/zenrows.log file for detailed API logs\n";
    
} catch (Exception $e) {
    echo "Error during testing: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
