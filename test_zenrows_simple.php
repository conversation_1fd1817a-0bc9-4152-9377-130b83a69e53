<?php
/**
 * Simple ZenRows API Test
 * Tests the basic ZenRows API functionality
 */

require_once 'config/config.php';

echo "=== Simple ZenRows API Test ===\n";

// Test the basic configuration from your provided code
$apiKey = '3dfd6205f79bbe078e3266970464a7c9e86c1a40';
$testUrl = 'https://httpbin.org/html';

echo "API Key: " . substr($apiKey, 0, 10) . "...\n";
echo "Test URL: {$testUrl}\n\n";

// Test basic request without any advanced features
$ch = curl_init();

$params = [
    'apikey' => $apiKey,
    'url' => $testUrl
];

$queryString = http_build_query($params);
$fullUrl = 'https://api.zenrows.com/v1/?' . $queryString;

echo "Full request URL: {$fullUrl}\n\n";

curl_setopt_array($ch, [
    CURLOPT_URL => $fullUrl,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_HEADER => true,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => false,
    CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
$error = curl_error($ch);

curl_close($ch);

echo "HTTP Code: {$httpCode}\n";
echo "cURL Error: " . ($error ?: 'None') . "\n";

if ($response !== false) {
    $headers = substr($response, 0, $headerSize);
    $content = substr($response, $headerSize);
    
    echo "Response Headers:\n";
    echo $headers . "\n";
    
    echo "Content Length: " . strlen($content) . " bytes\n";
    echo "Content Preview: " . substr($content, 0, 200) . "...\n";
    
    if ($httpCode === 200) {
        echo "\n✓ ZenRows API is working correctly!\n";
    } else {
        echo "\n✗ API returned error code: {$httpCode}\n";
        echo "Response content: " . substr($content, 0, 500) . "\n";
    }
} else {
    echo "\n✗ Failed to get response from API\n";
}

echo "\n=== Test Complete ===\n";
