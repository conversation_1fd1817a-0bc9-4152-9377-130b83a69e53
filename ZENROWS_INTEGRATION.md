# ZenRows API Integration for Booktoki Crawler

This document describes the ZenRows Universal Scraper API integration for crawling booktoki468.com in the Novel Translation Application.

## Overview

The ZenRows integration provides a robust solution for crawling protected websites like Booktoki that implement anti-bot measures. It uses premium proxies and JavaScript rendering to bypass these protections.

## Files Added/Modified

### New Files
- `config/zenrows-config.php` - ZenRows API configuration
- `classes/ZenRowsApiClient.php` - API client for ZenRows
- `crawlers/BooktokiCrawler.php` - Booktoki crawler using ZenRows
- `test_booktoki.php` - Test script for Booktoki crawler
- `test_zenrows_simple.php` - Simple ZenRows API test
- `manage_zenrows_config.php` - Configuration management script

### Modified Files
- `config/config.php` - Added ZenRows constants and Booktoki platform
- `classes/NovelManager.php` - Added Booktoki crawler support
- `index.php` - Added Booktoki to supported platforms
- `README.md` - Updated documentation

## Configuration

### API Key Management

The ZenRows API key is configured in `config/zenrows-config.php`:

```php
'api_key' => '****************************************', // Update when trial expires
```

### Platform-Specific Settings

Booktoki-specific settings are configured in the same file:

```php
'platform_settings' => [
    'booktoki' => [
        'js_render' => true,
        'premium_proxy' => true,
        'wait' => 5000,
        'wait_for' => '.novel-content, .chapter-content',
        'custom_headers' => [
            'Accept-Language' => 'ko-KR,ko;q=0.9,en;q=0.8',
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
        ]
    ]
]
```

## Usage

### Basic Crawler Usage

```php
require_once 'config/config.php';
require_once 'classes/BaseCrawler.php';
require_once 'classes/ZenRowsApiClient.php';
require_once 'crawlers/BooktokiCrawler.php';

$crawler = new BooktokiCrawler();

// Get novel information
$novelInfo = $crawler->getNovelInfo('https://booktoki468.com/novel/12345');

// Get chapter list
$chapters = $crawler->getChapterList('https://booktoki468.com/novel/12345');

// Get chapter content
$content = $crawler->getChapterContent('https://booktoki468.com/novel/12345/episode/1');
```

### Configuration Management

Use the configuration management script to update settings:

```bash
php manage_zenrows_config.php
```

This provides an interactive interface to:
- Update API key
- Toggle JS rendering
- Toggle premium proxy
- Update daily limits
- Test API connection

## Testing

### Run Basic Tests

```bash
# Test ZenRows API functionality
php test_zenrows_simple.php

# Test Booktoki crawler (requires real URLs)
php test_booktoki.php
```

### Test with Real URLs

To test with actual Booktoki content:

1. Find a real Booktoki novel URL (format: `booktoki468.com/novel/[number]`)
2. Update the test URL in `test_booktoki.php`
3. Uncomment the extraction tests
4. Run the test script

## Cost Management

### API Usage Tracking

The system automatically tracks:
- Request count
- Total cost
- Daily limits
- Remaining requests

### Cost Optimization

- **Basic requests**: $0.10 per 1,000 (Business plan)
- **JS rendering**: 5x cost multiplier
- **Premium proxies**: 10x cost multiplier
- **Both features**: 25x cost multiplier

### Daily Limits

Configure daily limits in `zenrows-config.php`:

```php
'cost_settings' => [
    'daily_request_limit' => 1000,
    'warn_at_percentage' => 80,
]
```

## Logging

### Log Files

- `logs/zenrows.log` - ZenRows API requests and responses
- `logs/error.log` - General application errors
- `logs/crawler.log` - Crawler-specific logs

### Log Monitoring

Monitor API usage and errors:

```bash
# View ZenRows logs
tail -f logs/zenrows.log

# View error logs
tail -f logs/error.log
```

## Error Handling

### Common Issues

1. **SSL Certificate Errors**
   - Solution: SSL verification disabled for development
   - Production: Use proper SSL certificates

2. **API Key Errors**
   - Check API key in `zenrows-config.php`
   - Verify key hasn't expired
   - Check account status

3. **Rate Limiting**
   - Monitor daily limits
   - Adjust request frequency
   - Upgrade plan if needed

4. **Content Not Found**
   - Verify URL format
   - Check if site structure changed
   - Update CSS selectors if needed

### Debugging

Enable detailed logging in `zenrows-config.php`:

```php
'error_handling' => [
    'log_requests' => true,
    'log_responses' => true, // Enable for debugging only
]
```

## Security Considerations

1. **API Key Protection**
   - Store in configuration file, not in code
   - Use environment variables in production
   - Rotate keys regularly

2. **SSL/TLS**
   - SSL verification disabled for development
   - Enable in production environments

3. **Rate Limiting**
   - Respect API limits
   - Implement exponential backoff
   - Monitor usage patterns

## Upgrading from Trial

When upgrading from trial to paid plan:

1. Update API key in `config/zenrows-config.php`
2. Adjust daily limits based on new plan
3. Update cost tracking settings
4. Test with new key

## Support

For issues with:
- **ZenRows API**: Contact ZenRows support with request ID
- **Crawler functionality**: Check logs and update selectors
- **Configuration**: Use `manage_zenrows_config.php` script

## Future Enhancements

Potential improvements:
- Automatic retry with exponential backoff
- Dynamic selector updating
- Multi-domain Booktoki support
- Advanced cost optimization
- Real-time usage monitoring dashboard
