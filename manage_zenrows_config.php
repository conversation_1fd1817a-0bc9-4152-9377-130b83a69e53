<?php
/**
 * ZenRows Configuration Management Script
 * Novel Translation Application
 * 
 * Use this script to easily update ZenRows API settings
 */

require_once 'config/config.php';

echo "=== ZenRows Configuration Manager ===\n";

$configFile = APP_ROOT . '/config/zenrows-config.php';

if (!file_exists($configFile)) {
    echo "Error: ZenRows configuration file not found!\n";
    exit(1);
}

// Load current configuration
$config = require $configFile;

echo "Current Configuration:\n";
echo "- API Key: " . substr($config['api_key'], 0, 10) . "...\n";
echo "- JS Rendering: " . ($config['default_settings']['js_render'] ? 'Enabled' : 'Disabled') . "\n";
echo "- Premium Proxy: " . ($config['default_settings']['premium_proxy'] ? 'Enabled' : 'Disabled') . "\n";
echo "- Daily Limit: " . $config['cost_settings']['daily_request_limit'] . " requests\n";
echo "- Timeout: " . $config['limits']['timeout'] . " seconds\n\n";

// Interactive configuration update
echo "Configuration Options:\n";
echo "1. Update API Key\n";
echo "2. Toggle JS Rendering\n";
echo "3. Toggle Premium Proxy\n";
echo "4. Update Daily Limit\n";
echo "5. Update Timeout\n";
echo "6. View Full Configuration\n";
echo "7. Test API Connection\n";
echo "8. Exit\n\n";

echo "Enter your choice (1-8): ";
$choice = trim(fgets(STDIN));

switch ($choice) {
    case '1':
        echo "Enter new API key: ";
        $newApiKey = trim(fgets(STDIN));
        if (!empty($newApiKey)) {
            updateConfigValue($configFile, 'api_key', $newApiKey);
            echo "✓ API key updated successfully!\n";
        } else {
            echo "✗ Invalid API key\n";
        }
        break;
        
    case '2':
        $current = $config['default_settings']['js_render'];
        $new = !$current;
        updateConfigValue($configFile, 'default_settings.js_render', $new);
        echo "✓ JS Rendering " . ($new ? 'enabled' : 'disabled') . "\n";
        break;
        
    case '3':
        $current = $config['default_settings']['premium_proxy'];
        $new = !$current;
        updateConfigValue($configFile, 'default_settings.premium_proxy', $new);
        echo "✓ Premium Proxy " . ($new ? 'enabled' : 'disabled') . "\n";
        break;
        
    case '4':
        echo "Enter new daily limit: ";
        $newLimit = (int)trim(fgets(STDIN));
        if ($newLimit > 0) {
            updateConfigValue($configFile, 'cost_settings.daily_request_limit', $newLimit);
            echo "✓ Daily limit updated to {$newLimit} requests\n";
        } else {
            echo "✗ Invalid limit\n";
        }
        break;
        
    case '5':
        echo "Enter new timeout (seconds): ";
        $newTimeout = (int)trim(fgets(STDIN));
        if ($newTimeout > 0) {
            updateConfigValue($configFile, 'limits.timeout', $newTimeout);
            echo "✓ Timeout updated to {$newTimeout} seconds\n";
        } else {
            echo "✗ Invalid timeout\n";
        }
        break;
        
    case '6':
        echo "\nFull Configuration:\n";
        print_r($config);
        break;
        
    case '7':
        echo "\nTesting API connection...\n";
        testApiConnection($config);
        break;
        
    case '8':
        echo "Goodbye!\n";
        break;
        
    default:
        echo "Invalid choice\n";
}

/**
 * Update a configuration value
 */
function updateConfigValue($configFile, $key, $value) {
    $config = require $configFile;
    
    // Handle nested keys like 'default_settings.js_render'
    $keys = explode('.', $key);
    $current = &$config;
    
    for ($i = 0; $i < count($keys) - 1; $i++) {
        $current = &$current[$keys[$i]];
    }
    
    $current[$keys[count($keys) - 1]] = $value;
    
    // Write back to file
    $configContent = "<?php\n/**\n * ZenRows API Configuration\n * Novel Translation Application\n * \n * This file contains ZenRows API settings that can be easily updated\n * when switching from trial to paid plans or changing API providers.\n */\n\nreturn " . var_export($config, true) . ";";
    
    file_put_contents($configFile, $configContent);
}

/**
 * Test API connection
 */
function testApiConnection($config) {
    try {
        require_once 'classes/ZenRowsApiClient.php';
        
        $client = new ZenRowsApiClient();
        $response = $client->makeRequest('https://httpbin.org/html', [
            'js_render' => false,
            'premium_proxy' => false
        ]);
        
        if ($response['success']) {
            echo "✓ API connection successful!\n";
            echo "- Status: {$response['status_code']}\n";
            echo "- Cost: {$response['cost']}\n";
            echo "- Request ID: {$response['request_id']}\n";
            
            $stats = $client->getUsageStats();
            echo "- Requests made: {$stats['requests_made']}\n";
            echo "- Remaining: {$stats['remaining_requests']}\n";
        } else {
            echo "✗ API connection failed\n";
        }
        
    } catch (Exception $e) {
        echo "✗ API test failed: " . $e->getMessage() . "\n";
    }
}
