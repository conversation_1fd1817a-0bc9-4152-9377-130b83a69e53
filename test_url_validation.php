<?php
require_once 'config/config.php';

$testUrls = [
    'https://booktoki468.com/novel/12345',
    'https://booktoki123.com/novel/67890',
    'https://kakuyomu.jp/works/16818093089916140901',
    'https://69shuba.cx/book/44425.htm',
    'https://invalid-site.com/novel/123'
];

echo "=== URL Validation Test ===\n";

foreach ($testUrls as $url) {
    $platform = validateUrl($url);
    echo "URL: {$url}\n";
    echo "Platform: " . ($platform ?: 'Not supported') . "\n\n";
}
