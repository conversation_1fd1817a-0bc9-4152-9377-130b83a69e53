[2025-06-14 07:18:33] [ZenRows] [info] Making ZenRows request to: https://httpbin.org/html
[2025-06-14 07:18:34] [ZenRows] [error] ZenRows request failed: cURL error: SSL certificate problem: unable to get local issuer certificate
[2025-06-14 07:18:58] [ZenRows] [info] Making ZenRows request to: https://httpbin.org/html
[2025-06-14 07:18:59] [ZenRows] [error] ZenRows request failed: ZenRows API error (HTTP 400)
[2025-06-14 07:20:14] [ZenRows] [info] Making ZenRows request to: https://httpbin.org/html
[2025-06-14 07:20:15] [ZenRows] [error] API Error Details - URL: https://api.zenrows.com/v1/?js_render=0&premium_proxy=0&wait=3000&block_resources=image%2Cfont%2Cmedia&original_status=1&apikey=3dfd6205f79bbe078e3266970464a7c9e86c1a40&url=https%3A%2F%2Fhttpbin.org%2Fhtml, Response: {"code":"REQS004","instance":"/v1","status":400,"title":"Invalid value provided for 'js_render' parameter; invalid boolean value '0' (REQS004)","type":"https://docs.zenrows.com/api-error-codes#REQS004"}
[2025-06-14 07:20:15] [ZenRows] [error] ZenRows request failed: ZenRows API error (HTTP 400): {"code":"REQS004","instance":"/v1","status":400,"title":"Invalid value provided for 'js_render' parameter; invalid boolean value '0' (REQS004)","type":"https://docs.zenrows.com/api-error-codes#REQS004"}
[2025-06-14 07:20:37] [ZenRows] [info] Making ZenRows request to: https://httpbin.org/html
[2025-06-14 07:20:39] [ZenRows] [info] Request #1, Cost: 0.001, Total: 0.001
[2025-06-14 07:24:06] [ZenRows] [info] Making ZenRows request to: https://booktoki468.com/novel/13405017
[2025-06-14 07:25:06] [ZenRows] [error] ZenRows request failed: cURL error: Operation timed out after 60015 milliseconds with 0 bytes received
