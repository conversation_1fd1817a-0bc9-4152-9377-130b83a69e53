# Novel Translation Application

A comprehensive PHP 8.2 web application for crawling and translating novels from Japanese and Chinese web novel platforms using Google Gemini AI.

## Features

### Core Functionality
- **Novel Detection & Preview**: Crawl and display novel information from supported platforms
- **AI Translation**: Powered by Google Gemini AI 2.5-flash for high-quality translations
- **Chapter Management**: Save and translate individual chapters with progress tracking
- **Smart Name Recognition**: Automatically detect and maintain consistency for character names, locations, and other proper nouns
- **Name Dictionary System**: Editable dictionary for translation consistency across chapters
- **Re-translate Feature**: Clear and re-translate existing chapters for improved quality
- **Bulk Operations**: Select multiple chapters for batch translation, re-translation, or clearing
- **Translation Management**: Delete translation results while preserving original content

### Supported Platforms
- **Kakuyomu** (kakuyomu.jp) - Japanese web novels ✅ Fully supported
- **Syosetu** (ncode.syosetu.com) - Japanese web novels ✅ Fully supported
- **69书吧** (69shuba.cx) - Chinese web novels ✅ Fully supported
- **Booktoki** (booktoki468.com) - Korean web novels ✅ Fully supported (via ZenRows API)

### Technical Features
- PHP 8.2 backend with MySQL database
- Responsive web interface using Bootstrap 5
- RESTful API architecture
- Robust error handling and logging
- Translation progress tracking
- Name consistency management

## Requirements

- **PHP 8.2+** with extensions:
  - PDO MySQL
  - cURL
  - DOM
  - libxml
- **MySQL 5.7+** or **MariaDB 10.3+**
- **Web server** (Apache/Nginx)
- **Google Gemini AI API key**

## Installation

### 1. Clone/Download the Application
Place the application files in your web server directory (e.g., `htdocs/wc/`).

### 2. Configure Database
Edit `config/database.php` if needed to match your MySQL settings:
```php
private const DB_HOST = 'localhost';
private const DB_NAME = 'novel_translator';
private const DB_USER = 'root';
private const DB_PASS = '';
```

### 3. Set API Key
The application now uses **DeepSeek AI** as the primary translation service with Gemini as fallback. The API keys are configured in `config/config.php`:

**Primary (DeepSeek AI):**
```php
define('DEEPSEEK_API_KEY', '***********************************');
define('DEEPSEEK_API_URL', 'https://api.deepseek.com/chat/completions');
```

**ZenRows API (for Booktoki):**
```php
// Edit config/zenrows-config.php to update the API key
'api_key' => 'your-zenrows-api-key-here'
```

**Fallback (Google Gemini AI):**
```php
define('GEMINI_API_KEY', 'AIzaSyDD7hE-_hLWbw1WbMqKWhDwD9U9bpOdX0g');
```

> **Note**: DeepSeek provides better reliability and performance compared to Gemini's recent rate limiting issues.

### 4. Run Database Setup

**Option A: Setup Guide (Recommended)**
Navigate to `http://localhost/wc/setup-guide.php` for an interactive setup guide that will help you choose the best setup method.

**Option B: Direct Setup**
- **Regular Setup**: `http://localhost/wc/setup.php` - Standard setup with progress tracking
- **Standalone Setup**: `http://localhost/wc/setup-standalone.php` - Independent setup (use if regular setup fails)

**Important Notes:**
- The setup script will create the `novel_translator` database automatically
- If you encounter the "Table doesn't exist" error, use the **Standalone Setup**
- The setup process includes real-time progress updates and detailed error messages

### 5. Verify Installation
After setup, you can verify everything is working:
- **Setup Guide**: `http://localhost/wc/setup-guide.php` - Interactive guide with status check
- **Database Check**: `http://localhost/wc/verify-db.php` - Database verification

### 6. Start Using
Go to `http://localhost/wc/` to access the main application.

## Usage Guide

### 1. Preview a Novel
1. Enter a novel URL from one of the supported platforms
2. Click "Preview Novel" to crawl and display novel information
3. Review the translated title, synopsis, and chapter list
4. Click "Save Novel" to add it to your library

### 2. Manage Chapters
1. Click "View" on any saved novel to open the details page
2. Use "Save" button to download original chapter content
3. Use "Translate" button to translate saved chapters
4. Use "Bulk Select" for multi-chapter operations (translate, re-translate, clear)
5. Use "Re-translate" button to improve existing translations
6. Use "Clear" button to remove translations while keeping original text
7. Monitor translation progress and status

### 3. Edit Name Dictionary
1. In the novel details modal, view the Name Dictionary panel
2. Edit translations for detected names to ensure consistency
3. Names are automatically detected during translation and can be manually refined

## API Endpoints

### Preview Novel
```
GET /api/preview.php?url=<novel_url>
```

### Novel Management
```
GET /api/novels.php                    # Get saved novels
POST /api/novels.php                   # Save novel
PUT /api/novels.php                    # Update novel
```

### Chapter Management
```
GET /api/chapters.php?novel_id=<id>    # Get novel details with chapters
POST /api/chapters.php                 # Save chapter content
PUT /api/chapters.php                  # Translate chapter
```

### Name Dictionary
```
PUT /api/names.php                     # Update name translation
```

## Database Schema

### Main Tables
- **novels** - Novel metadata and information
- **chapters** - Chapter content (original and translated)
- **name_dictionary** - Name mappings for translation consistency
- **translation_logs** - Translation history and API usage tracking
- **user_preferences** - Application settings

## File Structure

```
/
├── config/
│   ├── config.php          # Main configuration
│   └── database.php        # Database connection class
├── classes/
│   ├── BaseCrawler.php     # Base crawler functionality
│   ├── NovelManager.php    # Novel management logic
│   ├── TranslationService.php # AI translation service (DeepSeek + Gemini fallback)
│   └── DeepSeekTranslationService.php # DeepSeek AI translation service
├── crawlers/
│   ├── KakuyomuCrawler.php # Kakuyomu platform crawler
│   ├── SyosetuCrawler.php  # Syosetu platform crawler
│   ├── Shuba69Crawler.php  # 69书吧 platform crawler
│   └── BooktokiCrawler.php # Booktoki platform crawler (ZenRows API)
├── api/
│   ├── preview.php         # Novel preview endpoint
│   ├── novels.php          # Novel management endpoint
│   ├── chapters.php        # Chapter management endpoint
│   └── names.php           # Name dictionary endpoint
├── assets/
│   ├── css/style.css       # Application styles
│   └── js/app.js           # Frontend JavaScript
├── sql/
│   └── schema.sql          # Database schema
├── logs/                   # Application logs (auto-created)
├── index.php               # Main interface
├── setup.php               # Database setup script
├── setup-guide.php         # Interactive setup guide
├── verify-db.php           # Database verification script
└── README.md               # This file
```

## Troubleshooting

### Common Issues

1. **Database Setup Errors**
   - **"Table doesn't exist"**: Run `setup.php` first to create the database
   - **"Access denied"**: Check MySQL credentials in `config/database.php`
   - **"Connection refused"**: Ensure MySQL server is running
   - Use `verify-db.php` for detailed database diagnostics

2. **Database Connection Failed**
   - Check MySQL server is running on localhost:3306
   - Verify database credentials in `config/database.php`
   - Ensure MySQL user has CREATE DATABASE privileges
   - Run `verify-db.php` to diagnose connection issues

3. **Translation API Errors**
   - **DeepSeek API Issues**: Check DeepSeek API key configuration in `config/config.php`
   - **Gemini Fallback Issues**: Verify Gemini API key is valid (used as fallback)
   - Check internet connectivity
   - Review API usage limits and rate limiting
   - Check `debug.log` for "DeepSeek translation successful" messages

4. **Crawling Failures**
   - Websites may have changed their structure
   - Check if the target website is accessible
   - Review crawler logs in `logs/crawler.log`

5. **PHP Extension Missing**
   - Install required PHP extensions (cURL, PDO, DOM)
   - Restart web server after installing extensions

### Logs
- **Error logs**: `logs/error.log`
- **Crawler logs**: `logs/crawler.log`
- **Translation logs**: Stored in database `translation_logs` table

## Development

### Adding New Platforms
1. Create a new crawler class extending `BaseCrawler`
2. Implement required abstract methods
3. Add platform configuration to `config/config.php`
4. Update URL validation in helper functions

### ZenRows API Configuration
For sites with anti-bot protection (like Booktoki), the application uses ZenRows Universal Scraper API:

1. **Update API Key**: Edit `config/zenrows-config.php`
2. **Platform Settings**: Configure platform-specific options in the same file
3. **Cost Management**: Monitor usage through built-in tracking
4. **Logs**: Check `logs/zenrows.log` for API request details

**Key Features:**
- JavaScript rendering for dynamic content
- Premium proxies for anti-bot bypass
- Automatic retry logic with exponential backoff
- Cost tracking and daily limits
- Platform-specific configurations

### Customizing Translation
- Modify translation prompts in `TranslationService.php`
- Adjust name detection patterns in `config/config.php`
- Configure translation parameters (temperature, tokens, etc.)

## Security Notes

- API key is included in the code for demo purposes
- In production, store sensitive credentials in environment variables
- Implement rate limiting for API endpoints
- Add user authentication if needed
- Validate and sanitize all user inputs

## License

This application is provided as-is for educational and personal use.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review application logs
3. Verify all requirements are met
4. Test with the provided example URLs
