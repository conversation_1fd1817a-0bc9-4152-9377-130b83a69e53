<?php
/**
 * ZenRows API Client
 * Novel Translation Application
 * 
 * Handles communication with ZenRows Universal Scraper API
 */

class ZenRowsApiClient {
    private $config;
    private $apiKey;
    private $apiUrl;
    private $requestCount = 0;
    private $totalCost = 0;
    
    public function __construct() {
        $this->loadConfig();
        $this->apiKey = $this->config['api_key'];
        $this->apiUrl = $this->config['api_url'];
    }
    
    /**
     * Load ZenRows configuration
     */
    private function loadConfig() {
        $configFile = APP_ROOT . '/config/zenrows-config.php';
        if (!file_exists($configFile)) {
            throw new Exception("ZenRows configuration file not found: {$configFile}");
        }
        
        $this->config = require $configFile;
        
        if (empty($this->config['api_key'])) {
            throw new Exception("ZenRows API key not configured");
        }
    }
    
    /**
     * Make a request to ZenRows API
     */
    public function makeRequest(string $url, array $options = []): array {
        $this->validateDailyLimit();

        // Start with basic required parameters
        $params = [
            'apikey' => $this->apiKey,
            'url' => $url
        ];

        // Apply platform-specific settings if available
        if (isset($options['platform']) && isset($this->config['platform_settings'][$options['platform']])) {
            $platformSettings = $this->config['platform_settings'][$options['platform']];
            // Remove custom_headers from params as they need special handling
            $customHeaders = $platformSettings['custom_headers'] ?? [];
            unset($platformSettings['custom_headers']);
            $params = array_merge($params, $platformSettings);
        }

        // Merge with default settings (excluding custom_headers)
        $defaultSettings = $this->config['default_settings'];
        unset($defaultSettings['custom_headers']);
        $params = array_merge($defaultSettings, $params);

        // Override with any specific options passed
        $params = array_merge($params, $options);

        // Remove platform key as it's not a ZenRows parameter
        unset($params['platform']);

        // Convert boolean values to strings for ZenRows API
        foreach ($params as $key => $value) {
            if (is_bool($value)) {
                $params[$key] = $value ? 'true' : 'false';
            }
        }

        $this->log("Making ZenRows request to: {$url}");

        try {
            $response = $this->executeRequest($params);
            $this->trackUsage($response);

            return [
                'success' => true,
                'content' => $response['content'],
                'status_code' => $response['status_code'],
                'headers' => $response['headers'],
                'cost' => $response['cost'] ?? 0,
                'request_id' => $response['request_id'] ?? null
            ];

        } catch (Exception $e) {
            $this->log("ZenRows request failed: " . $e->getMessage(), 'error');
            throw $e;
        }
    }
    
    /**
     * Execute the actual HTTP request to ZenRows
     */
    private function executeRequest(array $params): array {
        $ch = curl_init();
        
        // Build query string
        $queryString = http_build_query($params);
        $fullUrl = $this->apiUrl . '?' . $queryString;
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $fullUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->config['limits']['timeout'],
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 5,
            CURLOPT_USERAGENT => USER_AGENT,
            CURLOPT_HEADER => true,
            CURLOPT_ENCODING => 'gzip,deflate', // Enable compression
            CURLOPT_SSL_VERIFYPEER => false, // Disable SSL verification for development
            CURLOPT_SSL_VERIFYHOST => false, // Disable SSL host verification for development
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($error) {
            throw new Exception("cURL error: {$error}");
        }
        
        if ($response === false) {
            throw new Exception("Failed to get response from ZenRows API");
        }
        
        // Parse headers and content
        $headers = substr($response, 0, $headerSize);
        $content = substr($response, $headerSize);
        
        // Parse response headers
        $parsedHeaders = $this->parseHeaders($headers);
        
        // Check for API errors
        if ($httpCode >= 400) {
            $errorMessage = "ZenRows API error (HTTP {$httpCode})";
            if (!empty($content)) {
                $errorData = json_decode($content, true);
                if (isset($errorData['error'])) {
                    $errorMessage .= ": " . $errorData['error'];
                } else {
                    // If not JSON, include raw content for debugging
                    $errorMessage .= ": " . substr($content, 0, 500);
                }
            }
            $this->log("API Error Details - URL: {$fullUrl}, Response: {$content}", 'error');
            throw new Exception($errorMessage);
        }
        
        return [
            'content' => $content,
            'status_code' => $httpCode,
            'headers' => $parsedHeaders,
            'cost' => $parsedHeaders['X-Request-Cost'] ?? 0,
            'request_id' => $parsedHeaders['X-Request-Id'] ?? null
        ];
    }
    
    /**
     * Parse HTTP headers into associative array
     */
    private function parseHeaders(string $headers): array {
        $parsed = [];
        $lines = explode("\r\n", $headers);
        
        foreach ($lines as $line) {
            if (strpos($line, ':') !== false) {
                list($key, $value) = explode(':', $line, 2);
                $parsed[trim($key)] = trim($value);
            }
        }
        
        return $parsed;
    }
    
    /**
     * Track API usage and costs
     */
    private function trackUsage(array $response): void {
        $this->requestCount++;
        $this->totalCost += floatval($response['cost'] ?? 0);
        
        if ($this->config['cost_settings']['enable_cost_tracking']) {
            $this->log("Request #{$this->requestCount}, Cost: {$response['cost']}, Total: {$this->totalCost}");
        }
        
        // Check daily limits
        $dailyLimit = $this->config['cost_settings']['daily_request_limit'];
        $warnPercentage = $this->config['cost_settings']['warn_at_percentage'];
        
        if ($this->requestCount >= ($dailyLimit * $warnPercentage / 100)) {
            $this->log("Warning: Approaching daily request limit ({$this->requestCount}/{$dailyLimit})", 'warning');
        }
    }
    
    /**
     * Validate daily request limits
     */
    private function validateDailyLimit(): void {
        $dailyLimit = $this->config['cost_settings']['daily_request_limit'];
        
        if ($this->requestCount >= $dailyLimit) {
            throw new Exception("Daily request limit reached ({$dailyLimit} requests)");
        }
    }
    
    /**
     * Get current usage statistics
     */
    public function getUsageStats(): array {
        return [
            'requests_made' => $this->requestCount,
            'total_cost' => $this->totalCost,
            'daily_limit' => $this->config['cost_settings']['daily_request_limit'],
            'remaining_requests' => $this->config['cost_settings']['daily_request_limit'] - $this->requestCount
        ];
    }
    
    /**
     * Log messages
     */
    private function log(string $message, string $level = 'info'): void {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] [ZenRows] [{$level}] {$message}";
        
        // Log to file
        $logFile = APP_ROOT . '/logs/zenrows.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($logFile, $logMessage . PHP_EOL, FILE_APPEND | LOCK_EX);
        
        // Also log errors to main error log
        if ($level === 'error') {
            logError("ZenRows: {$message}");
        }
    }
}
